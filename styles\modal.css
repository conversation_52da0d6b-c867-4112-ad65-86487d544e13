/* Modal Component Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-dialog {
  background-color: #111727;
  border-radius: 15px;
  padding: 30px;
  max-width: 450px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  transform: scale(0.8) translateY(-50px);
  transition: all 0.3s ease;
  border: 1px solid rgba(54, 226, 236, 0.2);
}

.modal-overlay.show .modal-dialog {
  transform: scale(1) translateY(0);
}

.modal-header {
  text-align: center;
  margin-bottom: 25px;
}

.modal-header .modal-icon {
  font-size: 48px;
  color: #36e2ec;
  margin-bottom: 15px;
  display: block;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin: 0;
}

.modal-body {
  text-align: center;
  margin-bottom: 30px;
}

.modal-body p {
  font-size: 16px;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.modal-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.modal-btn-primary {
  background-color: #36e2ec;
  color: #111727;
}

.modal-btn-primary:hover {
  background-color: #2bc5ce;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(54, 226, 236, 0.3);
}

.modal-btn-secondary {
  background-color: rgba(105, 105, 170, 0.7);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-btn-secondary:hover {
  background-color: rgba(105, 105, 170, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(105, 105, 170, 0.3);
}

/* Responsive styles */
@media (max-width: 576px) {
  .modal-dialog {
    padding: 25px 20px;
    margin: 20px;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .modal-btn {
    width: 100%;
  }
  
  .modal-header .modal-icon {
    font-size: 40px;
  }
  
  .modal-header h3 {
    font-size: 18px;
  }
  
  .modal-body p {
    font-size: 15px;
  }
}
